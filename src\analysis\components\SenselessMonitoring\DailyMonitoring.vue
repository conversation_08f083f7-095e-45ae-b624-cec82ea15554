<!-- 日常监测 -->
<template>
  <div class="daily-monitoring">
    <div class="environment-title">
      <div class="environment-item">
        <img src="../../assets/images/SenselessMonitoring/number-icon-0.png" alt="" srcset="">
        <div class="item-number" style="padding-left: 0px;cursor: pointer;" @click="openDialog('quota')">
          <animate-number
            v-for="(item, index) in jcpjdData.yjzb"
            :key="'autoGet' + index"
            style="color:#f4af1b;font-weight:600;"
            from="1"
            :to="item"
          />
        </div>
        <span>大环境</span>
      </div>
      <div class="environment-item">
        <img src="../../assets/images/SenselessMonitoring/number-icon-1.png" alt="" srcset="">
        <span>二级指标</span>
        <div class="item-number" @click="openDialog('quota')">
          <animate-number
            v-for="(item, index) in jcpjdData.ejzb"
            :key="'autoGet2' + index"
            style="color:#f4af1b;font-weight:600;"
            from="1"
            :to="item"
          />
        </div>
        <span>个</span>
      </div>
      <div class="environment-item">
        <img src="../../assets/images/SenselessMonitoring/number-icon-1.png" alt="" srcset="">
        <span>三级指标</span>
        <div class="item-number" @click="openDialog('quota')">
          <animate-number
            v-for="(item, index) in jcpjdData.sjzb"
            :key="'autoGet3' + index"
            style="color:#f4af1b;font-weight:600;"
            from="1"
            :to="item"
          />
        </div>
        <span>个</span>
      </div>
      <div class="environment-item">
        <img src="../../assets/images/SenselessMonitoring/number-icon-3.png" alt="" srcset="">
        <span>监测评价点</span>
        <el-popover placement="bottom" trigger="hover" width="160" popper-class="popover-class">
          <div class="popover-content">
            <div>
              <label>自动采集：</label>
              <span style="cursor: pointer;" @click="openDialog('collect','1')">

                <animate-number
                  :key="'1-autoGet'"
                  style="color:#f4af1b;font-weight:600;font-size:18px;"
                  from="1"
                  :to="jcpjdData.zdcj"
                />
              </span>
            </div>
            <div>
              <label>手动填报：</label>
              <span style="cursor: pointer;" @click="openDialog('collect','3')">
                <animate-number
                  :key="'2-autoGet'"
                  style="color:#f4af1b;font-weight:600;font-size:18px;"
                  from="1"
                  :to="jcpjdData.sdtb"
                />
              </span>
            </div>
          </div>
          <div slot="reference" class="item-number" @click="openDialog('submitted')">
            <animate-number
              v-for="(item, index) in jcpjdData.jcpjd"
              :key="'autoGet' + index"
              style="color:#f4af1b;font-weight:600;"
              from="1"
              :to="item"
            />
          </div>
        </el-popover>
        <span>个</span>
      </div>
    </div>
    <div class="content">
      <div class="content-top">
        <panel-item title="各区监测情况" theme="no-background map">
          <EachDistrictMap :init-date="initDate" />
        </panel-item>
        <div class="chart-right">
          <panel-item title="营商环境指数" theme="background border">
            <LineChartParentAll :chart-data="lineChartData" style="height: 100%;" />
          </panel-item>
          <panel-item title="分环境营商指数" theme="background border">
            <GaugeChart :list="gaugeList" />
          </panel-item>
          <panel-item title="" theme="no-background table-item">
            <template slot="title">
              指标指数Top5
            </template>
            <table v-if="dataTopList" class="mytable table-four table-five" cellspacing="0" cellpadding="0">
              <thead>
                <tr>
                  <th style="width:3vw">排名</th>
                  <th>指标</th>
                  <th class="qoqRate-td" style="width:5vw">指数值</th>
                  <th class="qoqRate-td" style="width:5vw">表现值</th>
                </tr>
              </thead>
              <tbody v-if="dataTopList && dataTopList.length > 0">
                <tr v-for="(v, n) in dataTopList" :key="'x' + n">
                  <td>{{ n+1 }}</td>
                  <td>
                    <TooltipWText class="quota-name" :text="v.quotaName" />
                  </td>
                  <td>{{ v.hb }}</td>
                  <td>{{ v.bxz }}</td>
                </tr>
              </tbody>
              <tr v-else class="nothing-top">
                <td colspan="4">
                  暂无变化
                </td>
              </tr>
            </table>
          </panel-item>

          <panel-item title="" theme="no-background table-item">
            <template slot="title">
              指标指数Last5
            </template>
            <table v-if="dataLastList" class="mytable table-four table-five" cellspacing="0" cellpadding="0">
              <thead>
                <tr>
                  <th style="width:3vw">排名</th>
                  <th>指标</th>
                  <th class="qoqRate-td" style="width:5vw">指数值</th>
                  <th class="qoqRate-td" style="width:5vw">表现值</th>
                </tr>
              </thead>
              <tbody v-if="dataLastList && dataLastList.length > 0">
                <tr v-for="(v, n) in dataLastList" :key="'x' + n">
                  <td>{{ n+1 }}</td>
                  <td>
                    <TooltipWText class="quota-name" :text="v.quotaName" />
                  </td>
                  <td>{{ v.hb }}</td>
                  <td>{{ v.bxz }}</td>
                </tr>
              </tbody>
              <tr v-else class="nothing-top">
                <td colspan="4">
                  暂无变化
                </td>
              </tr>
            </table>
          </panel-item>

        </div>
      </div>
      <div class="content-bottom">
        <panel-item title="市级监测问题整改情况" theme="no-background within-border" :more="true">
          <div slot="right" class="d-flex">
            <div class="table-query-select">
              <el-select
                v-model="cityQuarter"
                class=""
                style="width: 160px;"
                :popper-append-to-body="true"
                popper-class="success-dictionary-select-popper"
                size="mini"
                placeholder="请选择"
                @change="(val) => taskSuccessCnt(val, 'city')"
              >
                <el-option v-for="(item, i) in quarterList" :key="i" :label="item.dmName" :value="item.dmValue" />
              </el-select>
            </div>
          </div>
          <div style="width: 100%; height: calc(100%)">
            <shi :chart-data="cityChartData" />
          </div>
        </panel-item>
        <panel-item title="区级监测问题整改情况" theme="no-background within-border" :more="true">
          <div slot="right" class="d-flex">
            <div class="table-query-select">
              <el-select
                v-model="areaQuarter"
                class=""
                style="width: 160px;"
                :popper-append-to-body="true"
                popper-class="success-dictionary-select-popper"
                size="mini"
                placeholder="请选择"
                @change="(val) => taskSuccessCnt(val, 'area')"
              >
                <el-option v-for="(item, i) in quarterList" :key="i" :label="item.dmName" :value="item.dmValue" />
              </el-select>
            </div>
          </div>
          <div style="width: 100%; height: calc(100%)">
            <qusuccessbar :chart-data="areaChartData" />
          </div>
        </panel-item>
      </div>
    </div>
  </div>
</template>

<script>

import { getIndexHistoryTrend, getCityScoreInfo, getProblemSelectDict, getCityProblemSituation, getAreaProblemSituation } from '../../api/SenselessMonitoring'
import GaugeChart from './GaugeChart.vue'
import EachDistrictMap from './EachDistrictMap.vue'
import LineChartParentAll from '../LineChartParentAll.vue'
import Qusuccessbar from './qusuccessbar.vue'
import Shi from './shi.vue'

export default {
  name: 'DailyMonitoring',
  components: {
    EachDistrictMap,
    LineChartParentAll,
    GaugeChart,
    Qusuccessbar,
    Shi
  },
  props: {
    initDate: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      gaugeList: null,
      cityQuarter: '',
      areaQuarter: '',
      lineChartData: {
        xData: [],
        yData: [],
        legendData: [],
        yAxisName: '指数',
        lineColor: '#f8d151',
        unit: '分',
        top: '60',
        legendTop: '15%',
        legend: ['指数']
      },
      formData: {
        firstQuotaCnt: [],
        secondQuotaCnt: [],
        thirdQuotaCnt: [],
        fourthQuotaCnt: []
      },
      cityChartData: null,
      areaChartData: null,
      dataLastList: [],
      dataTopList: [],
      quarterList: [],
      jcpjdData: {}
    }
  },
  computed: {

  },
  watch: {
    initDate: {
      handler(val) {
        if (val) {
          this.cityQuarter = val.year + '-' + val.quarter
          this.areaQuarter = val.year + '-' + val.quarter
          this.init()
          const list = JSON.parse(JSON.parse(window.sessionStorage.getItem('chartOotion'))?.['wgjc.top5Last5'] || 'null')
          if (list) {
            this.dataTopList = list[this.cityQuarter].top5
            this.dataLastList = list[this.cityQuarter].last5
          }
        }
      },
      immediate: true
    }
  },
  created() {
    const data = JSON.parse(JSON.parse(window.sessionStorage.getItem('chartOotion'))?.['wgjc.rcjc.jcpjd'] || 'null')

    if (data.root) {
      this.jcpjdData = {
        ...data.root,
        yjzb: String(data.root.yjzb).split(''),
        ejzb: String(data.root.ejzb).split(''),
        sjzb: String(data.root.sjzb).split(''),
        jcpjd: String(data.root.jcpjd).split('')
      }
    }
  },
  mounted() {
    // getQuotaCount('01').then((res) => {
    //   this.formData = {
    //     firstQuotaCnt: String(res.firstQuotaCnt).split(''),
    //     secondQuotaCnt: String(res.secondQuotaCnt).split(''),
    //     thirdQuotaCnt: String(res.thirdQuotaCnt).split(''),
    //     fourthQuotaCnt: String(res.fourthQuotaCnt).split('')
    //   }
    // })
  },
  methods: {
    init() {
      getProblemSelectDict(this.initDate).then((res) => {
        this.quarterList = res
      })
      // 六大环境指数历史趋势
      getIndexHistoryTrend(this.initDate).then((res) => {
        this.lineChartData.xData = res.xaxisData
        this.lineChartData.yData = []
        this.lineChartData.legendData = res.legendData
        this.lineChartData.seriesData = res.seriesData
        res.seriesData.forEach(item => {
          this.lineChartData.yData.push(...item)
        })
      })
      // 指标表现值环比变化 top5, last5
      // getQuotaValueChange(this.initDate).then((res) => {
      //   this.dataLastList = res.dataLastList
      //   this.dataTopList = res.dataTopList
      // })
      // 分环境营商指数
      getCityScoreInfo(this.initDate).then((res) => {
        this.gaugeList = res
      })
      this.getCityChart(this.initDate)
      this.getAreaChart(this.initDate)
    },
    getCityChart(val) {
      // 市级监测问题整改情况
      getCityProblemSituation(val).then((res) => {
        this.cityChartData = res
      })
    },
    getAreaChart(val) {
      // 区级监测问题整改情况
      getAreaProblemSituation(val).then((res) => {
        this.areaChartData = res
      })
    },
    // 切换市级区级监测问题整改情况
    taskSuccessCnt(val, type) {
      if (val) {
        const item = this.quarterList.find((e) => e.dmValue === val)
        if (type === 'city') {
          this.getCityChart({ year: item.ext1, quarter: item.ext2 })
        } else {
          this.getAreaChart({ year: item.ext1, quarter: item.ext2 })
        }
      }
    },

    // 打开手动报送弹窗
    openDialog(type, value) {
      this.$emit('openDialog', { type: type, val: value || '' })
    }
  }
}
</script>

<style scoped lang='scss'>
.daily-monitoring {
  width: 100%;
  height: calc(100% - 40px);

  .environment-title {
    display: flex;
    width: 100%;
    height: 50px;
    align-items: center;
    justify-content: space-evenly;
    padding: 5px 200px;
    box-sizing: border-box;

    .environment-item {
      display: flex;
      align-items: center;

      >img {
        padding-right: 8px;
        height: 30px;

      }

      .item-number {
        padding: 0px 8px;
        cursor: pointer;
        >span {
          padding: 5px 9px;
          font-size: 30px;
          display: inline-block;
          background: url("../../assets/images/SenselessMonitoring/number-bg.png") no-repeat;
          background-size: 100% 100%;
        }
      }

    }
  }

  .content {
    height: calc(100% - 60px);

    .content-top {
      display: flex;
      height: 70%;

      .map {
        width: 30%;
        background: rgba(96, 143, 233, 0.15);
        border-radius: 10px;
        margin-right: 10px;
      }

      .chart-right {
        flex: 1;
        display: flex;
        flex-wrap: wrap;
        // grid-template-columns: repeat(2, 1fr);
        // grid-template-rows: repeat(2, 1fr);
        gap: 10px;
        .panel-item{
          width: calc(50% - 5px);
          height: calc(100% - 243px);
        }
      }
    }

    .content-bottom {
      display: flex;
      margin-top: 10px;
      height: 30%;
      gap: 10px;
    }
  }
}

.table-item {
  height: 235px !important;

  :deep(.pi-title) {
    padding-top: 10px !important;
    background-position: left 10px top 15px !important;
    >span{
      font-family: MicrosoftYaHei-Bold;
    }
  }
}

.subTitle {
  font-family: "PangmenZhengdao", serif !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  zoom: 1;
  letter-spacing: 2px;
  background-image: -webkit-linear-gradient(top,
      rgba(255, 174, 65, 1),
      rgba(251, 255, 65, 1));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-left: 2px;
}

.mytable {
  width: 100%;
  font-size: 16px;
  color: white;

  .nothing,
  .nothing-top {
    td {
      border-left: 1px solid #158be4;
      height: 138px;
      border-radius: 0 0 8px 8px;
      font-size: 14px;
      letter-spacing: 2px;
      color: rgba(255, 255, 255, 0.7);
    }
  }

  .nothing-top {
    td {
      height: 167px !important;
    }
  }

  // border-collapse: collapse;
  // border-spacing: 0;
  border-collapse: separate;
  border-spacing: 0;

  th,
  td {
    text-align: center;
    border-right: 1px solid #158be4;
    border-bottom: 1px solid #158be4;
  }

  thead {
    tr {
      height: 33px;
    }

    th:first-child {
      border-top-left-radius: 8px;
      border-left: 1px solid #158be4;
    }

    th:last-child {
      border-top-right-radius: 8px;
    }

    th {
      background: linear-gradient(180deg,
          #158ae4cc,
          rgba(21, 139, 228, 0.2) 100%);
    }
  }

  tr {
    height: 34px;
  }

  tbody tr td:first-child {
    border-left: 1px solid #158be4;
  }

  tbody tr:last-child {
    td:first-child {
      border-bottom-left-radius: 8px;
    }

    td:last-child {
      border-bottom-right-radius: 8px;
    }
  }

  tbody tr:nth-child(odd) {
    background-color: rgba(7, 43, 74, 0.5);
  }

  tbody tr:nth-child(even) {
    background-color: rgba(10, 60, 101, 0.5);
  }

  .align-left {
    max-width: 16vw;
    text-align: left;
    padding: 0 15px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  &.table-five tr {
    height: 33px;
  }
}
.within-border {
  :deep(.pi-title) {
    background-position: left 10px top 15px !important;
    padding-top: 10px;
  }
  :deep(.pi-more){
    top: 7px !important;
    .table-query-select .el-select {
      background: rgba(18,37,79,0.4);
      border-radius: 4px;
      box-shadow: inset 0 0 4px 0px #3682BE;
      border: 1px solid #3682BE;
      .el-input__inner{
        color: #ffffff;
      }
    }
  }
  :deep(.barbox) {
    // box-shadow: 0 0 16px 0 rgba(40, 121, 197, 0.5) inset;
    // border: 1px solid #3682BE;
    // border-radius: 10px;
    // background: rgba(16, 27, 67, 0.56);
    box-sizing: border-box;
    padding: 5px;
    padding-bottom: 0px;
    background: rgba(18,37,79,0.4);
    border-radius: 10px;
    border: 1px solid #3682BE;
  }
}
.quota-name{
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 19vw;
  display: inline-block;
  vertical-align: middle;
}
.border {
  border: 1px solid #3682BE;
}

.table-query-select {
  display: flex;
  align-items: center;
  align-items: center;
  justify-content: flex-end;

  :deep(.el-input__inner) {
    border: none !important;
    font-size: 15px;
    background: rgba(255, 255, 255, 0);
    color: #dad8fd;
  }

  :deep(.el-select-dropdown) {
    border: none;
    background-color: #0b246b;
    color: #ffffff;

    .popper__arrow {
      border-bottom-color: #0b246b;
    }

    .popper__arrow::after {
      border-bottom-color: #0b246b;
    }
  }

  :deep(.el-select) {
    width: 115px;
    border: 1px solid #dad8fd;
    border-radius: 4px;

    .el-select-dropdown__item.selected {
      color: #409eff;
    }

    .el-select-dropdown__item,
    .el-select__caret {
      color: #ffffff;
    }

    .el-select-dropdown__item.hover,
    .el-select-dropdown__item:hover {
      background-color: #2a3cdb;
    }
  }
}
</style>
<style lang='scss'>
.success-dictionary-select-popper {
  border: none;
  background-color: #0b246b;
  color: #ffffff;

  .popper__arrow {
    border-bottom-color: #0b246b !important;
  }

  .popper__arrow::after {
    border-bottom-color: #0b246b !important;
  }

  .el-select-dropdown__item.selected {
    color: #409eff;
  }

  .el-select-dropdown__item,
  .el-select__caret {
    color: #ffffff;
  }

  .el-select-dropdown__item.hover,
  .el-select-dropdown__item:hover {
    background-color: #2a3cdb;
  }
}
.popover-class{
  background-color: #143E78;
  border-color: #143E78;
  .popover-content{
    padding: 5px;
    display: flex;
    gap: 10px;
    flex-direction: column;

    >div{

      >label{
        font-size: 16px;
        color: #ffffff;
        display: inline-block;
        width: 90px;
        text-align: right;
      }
    }
  }
  .popper__arrow{
    border-bottom-color: #143E78 !important;
    &:after{
      border-bottom-color: #143E78 !important;
    }
  }
}
</style>
