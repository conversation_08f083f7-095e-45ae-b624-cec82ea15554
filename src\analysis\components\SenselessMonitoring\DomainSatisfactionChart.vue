<template>
  <div style="width: 100%;height: 100%">
    <div
      v-if="chartData && chartData.xaxis && chartData.xaxis.length > 0"
      :key="uid"
      ref="DomainSatisfactionChart"
      class="barbox"
    />
    <div v-else class="no-data-cont">
      暂无数据
    </div>
  </div>
</template>

<script>

export default {
  name: 'DomainSatisfactionChart',
  props: {
    chartData: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      uid: '11111',
      mycharts: null

    }
  },
  watch: {
    chartData: {
      handler(val) {
        this.uid = this.$uid()
        if (this.mycharts) {
          this.mycharts.clear()
        }
        if (val && val.xaxis && val.xaxis.length > 0) {
          this.$nextTick(() => {
            this.mycharts = this.$echarts.init(this.$refs.DomainSatisfactionChart)
            this.init()
          })
        }
      },
      deep: true,
      immediate: true
    }
  },
  destroyed() {
    window.removeEventListener('resize', this.resizeChart)
  },
  methods: {
    changedata() {
      setInterval(() => {
        this.init()
      }, 2000)
    },
    nowSize(val, initWidth = 1920) {
      const nowClientWidth = document.documentElement.clientWidth
      return val * (nowClientWidth / initWidth)
    },
    init() {
      const that = this
      const dataZoom = [{
        show: true,
        height: 7,
        xAxisIndex: [
          0
        ],
        bottom: '8%',
        start: 0,
        end: 40,
        handleIcon: 'path://M306.1,413c0,2.2-1.8,4-4,4h-59.8c-2.2,0-4-1.8-4-4V200.8c0-2.2,1.8-4,4-4h59.8c2.2,0,4,1.8,4,4V413z',
        handleSize: '110%',
        handleStyle: {
          color: '#d3dee5'

        },
        textStyle: {
          color: '#fff'
        },
        borderColor: '#90979c'
      }, {
        type: 'inside',
        show: true,
        height: 8,
        start: 1,
        end: 35
      }]
      const legend = []
      this.chartData.legend.map((e, i) => {
        legend.push(e + '')
      })
      const size = this.nowSize(14)
      const option = {
        dataZoom: this.chartData.xaxis.length > 10 ? dataZoom : [],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          confine: true,
          appendBody: true,
          backgroundColor: 'rgba(3, 59, 119, 1)',
          borderColor: 'rgba(33, 242, 196, 1)',
          textStyle: {
            color: '#fff',
            fontSize: this.nowSize(14)
          }
        },
        legend: {
          top: 0,
          itemWidth: 10,
          itemHeight: 10,
          data: legend,
          textStyle: {
            color: '#fff'
          }
        },
        xAxis: {
          type: 'category',
          data: that.chartData.xaxis,
          axisTick: {
            show: false
          },
          axisLabel: {
            color: '#fff',
            interval: 0,
            formatter: function (value) {
              let result = ''
              if (value.length <= 5) {
                result = value
              } else {
                const str = value.substring(0, 5)
                result = str + '...'
              }
              return result
            }
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, .4)'
            }
          }
        },
        yAxis: [
          {
            name: '（%）',
            min: 70,
            max: 100,
            interval: 10,
            nameTextStyle: {
              color: '#fff'
            },
            axisLabel: {
              color: 'rgba(255, 255, 255, .8)',
              interval: 0
            },
            type: 'value',
            splitLine: {
              show: false
            }
          },
          {
            show: false,
            axisLabel: {
              color: 'rgba(255, 255, 255, .8)',
              interval: 0,
              formatter: '{value}%'
            },
            type: 'value',
            splitLine: {
              show: false
            }
          }
        ],
        grid: {
          bottom: that.chartData.xaxis.length > 10 ? 35 : 10,
          left: 10,
          right: 10,
          top: 30,
          containLabel: true
        },
        series: [
          {
            name: that.chartData.legend[0] + '',
            type: 'bar',
            barWidth: size,
            data: that.chartData.lastList,
            itemStyle: {
              color: new that.$echarts.graphic.LinearGradient(
                0,
                0,
                0,
                1,
                [
                  {
                    offset: 0,
                    color: '#3381FF'
                  },
                  {
                    offset: 1,
                    color: '#66A1FF'
                  }
                ]
              ),
              borderRadius: [4, 4, 0, 0]
            }
          },
          {
            name: that.chartData.legend[1] + '',
            type: 'bar',
            barWidth: size,
            data: that.chartData.yaxis,
            itemStyle: {
              color: new that.$echarts.graphic.LinearGradient(
                0,
                0,
                0,
                1,
                [
                  {
                    offset: 0,
                    color: '#F7A54D'
                  },
                  {
                    offset: 1,
                    color: '#F7BD7E'
                  }
                ]
              ),
              borderRadius: [4, 4, 0, 0]
            }
          }
        ]
      }
      this.mycharts.setOption(option, true)
      this.mycharts.getZr().on('mousemove', () => {
        this.mycharts.getZr().setCursorStyle('default')
      })
      // this.changedata()
      window.addEventListener('resize', this.resizeChart)
    },
    resizeChart() {
      this.mycharts.resize()
    }
  }
}
</script>

<style lang="scss" scoped>
.barbox {
  width: 100%;
  height: 100%;
}

.no-data-cont {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #dddddd;

  :deep(div) {
    display: none !important;
  }
}
</style>
