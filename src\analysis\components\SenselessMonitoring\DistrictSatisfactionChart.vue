<template>
  <div style="width: 100%;height: 100%">
    <div
      v-if="chartData && chartData.xaxis && chartData.xaxis.length > 0"
      :key="uid"
      ref="DistrictSatisfactionChart"
      class="barbox"
    />
    <div v-else class="no-data-cont">
      暂无数据
    </div>
  </div>
</template>

<script>
export default {
  name: 'DistrictSatisfactionChart',
  props: {
    chartData: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      uid: 'abc',
      mycharts: null
    }
  },
  watch: {
    chartData: {
      handler(val) {
        this.uid = this.$uid()
        if (this.mycharts) {
          this.mycharts.clear()
        }
        if (val && val.xaxis && val.xaxis.length > 0) {
          this.$nextTick(() => {
            this.mycharts = this.$echarts.init(this.$refs.DistrictSatisfactionChart)
            this.init()
          })
        }
      },
      deep: true,
      immediate: true
    }
  },
  destroyed() {
    window.removeEventListener('resize', this.resizeChart)
  },
  methods: {
    changedata() {
      setInterval(() => {
        this.init()
      }, 2000)
    },
    nowSize(val, initWidth = 1920) {
      const nowClientWidth = document.documentElement.clientWidth
      return val * (nowClientWidth / initWidth)
    },
    init() {
      const size = this.nowSize(14)
      const legend = []
      this.chartData.legend.map((e, i) => {
        legend.push(e + '')
      })
      this.chartData.lastList.map((e) => {
        e = e.toFixed(2)
      })
      this.chartData.yaxis.map((e) => {
        e = e.toFixed(2)
      })

      const that = this
      const option = {
        dataZoom: {
          show: false
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          confine: true,
          backgroundColor: 'rgba(3, 59, 119, 1)',
          borderColor: 'rgba(33, 242, 196, 1)',
          textStyle: {
            color: '#fff',
            fontSize: this.nowSize(14)
          }
        },
        legend: {
          top: 0,
          itemWidth: 10,
          itemHeight: 10,
          data: legend,
          textStyle: {
            color: '#fff'
          }
        },
        xAxis: {
          type: 'category',
          data: this.chartData.xaxis,
          axisTick: {
            show: false
          },
          axisLabel: {
            color: '#fff',
            textStyle: {
              fontSize: 12
            },
            interval: 0,
            rotate: 30 // 新增：x轴标签倾斜40度
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, .4)'
            }
          }
        },
        yAxis: [
          {
            name: '（%）',
            min: 70,
            max: 100,
            interval: 10,
            nameTextStyle: {
              color: '#fff'
            },
            axisLabel: {
              color: 'rgba(255, 255, 255, .8)',
              interval: 0
            },
            type: 'value',
            splitLine: {
              show: false
            }
          },
          {
            show: false,
            axisLabel: {
              color: 'rgba(255, 255, 255, .8)',
              interval: 0,
              formatter: '{value}%'
            },
            type: 'value',
            splitLine: {
              show: false
            }
          }
        ],
        grid: {
          bottom: 10,
          left: 10,
          right: 40,
          top: 30,
          containLabel: true
        },
        series: [
          {
            name: this.chartData.legend[0] + '',
            type: 'bar',
            barMaxWidth: size,
            data: this.chartData.lastList,
            itemStyle: {
              color: new this.$echarts.graphic.LinearGradient(
                0,
                0,
                0,
                1,
                [
                  {
                    offset: 0,
                    color: '#3381FF'
                  },
                  {
                    offset: 1,
                    color: '#66A1FF'
                  }
                ]
              ),
              borderRadius: [4, 4, 0, 0]
            },
            markLine:
            {
              symbol: ['none', 'none'],
              silent: true,
              data: [{
                type: 'average',
                align: 'center',
                //  yAxis: e.value,
                name: '平均值',
                label: {
                  color: '#F8D152'
                },
                lineStyle: {
                  color: '#F8D152'
                }
              }],
              precision: 0,
              label: {
                normal: {
                  formatter: function (params) {
                    return `{name|${params.name}}\n{value|${that.chartData.avgScore}}`
                  },
                  textStyle: {
                    padding: [0, 0, 0, 30],
                    align: 'center'
                  },
                  rich: {
                    name: {
                    },
                    value: {
                      align: 'center'
                    }

                  }
                }
              }
            }
          },
          {
            name: this.chartData.legend[1] + '',
            type: 'bar',
            barMaxWidth: size,
            // data: this.list.map((v) => v.number),
            data: this.chartData.yaxis,
            itemStyle: {
              color: new this.$echarts.graphic.LinearGradient(
                0,
                0,
                0,
                1,
                [
                  {
                    offset: 0,
                    color: '#B469FF'
                  },
                  {
                    offset: 1,
                    color: '#CD9CFF'
                  }
                ]
              ),
              borderRadius: [4, 4, 0, 0]
            }
          }
        ]
      }
      this.mycharts.setOption(option)
      this.mycharts.getZr().on('mousemove', () => {
        this.mycharts.getZr().setCursorStyle('default')
      })
      // this.changedata()
      window.addEventListener('resize', this.resizeChart)
    },
    resizeChart() {
      this.mycharts.resize()
    }
  }
}
</script>

<style lang="scss" scoped>
.barbox {
  width: 100%;
  height: 100%;
}

.no-data-cont {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #dddddd;

  :deep(div) {
    display: none !important;
  }
}
</style>
