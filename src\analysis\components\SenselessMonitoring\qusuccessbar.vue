<template>
  <div style="width: 100%;height: 100%">
    <div
      v-if="chartData && chartData.xaxisData && chartData.xaxisData.length > 0"
      :key="uid"
      ref="barQu"
      class="barbox"
    />
    <div v-else class="no-data-cont">
      暂无数据
    </div>
  </div>
</template>

<script>
export default {
  name: 'PictorialBar',
  props: {
    chartData: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      uid: 'abc',
      mycharts: null
    }
  },
  watch: {
    chartData: {
      handler(val) {
        this.uid = this.$uid()
        if (this.mycharts) {
          this.mycharts.clear()
        }
        if (val && val.xaxisData && val.xaxisData.length > 0) {
          this.$nextTick(() => {
            this.mycharts = this.$echarts.init(this.$refs.barQu)
            this.init()
          })
        }
      },
      deep: true,
      immediate: true
    }
  },
  destroyed() {
    window.removeEventListener('resize', this.resizeChart)
  },
  methods: {
    changedata() {
      setInterval(() => {
        this.init()
      }, 2000)
    },
    nowSize(val, initWidth = 1920) {
      const nowClientWidth = document.documentElement.clientWidth
      return val * (nowClientWidth / initWidth)
    },
    init() {
      const size = this.nowSize(14)
      const seriesData = this.chartData.seriesData
      const finishNumber = []
      seriesData[0].map((e, i) => {
        finishNumber.push({
          value: e,
          itemStyle: {
            borderRadius: seriesData[1][i] && seriesData[1][i] !== 0 ? 0 : [4, 4, 0, 0]
          }
        })
      })

      const option = {
        dataZoom: {
          show: false
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          },
          confine: true,
          backgroundColor: 'rgba(3, 59, 119, 1)',
          borderColor: 'rgba(33, 242, 196, 1)',
          textStyle: {
            color: '#fff',
            fontSize: this.nowSize(14)
          }
        },
        legend: {
          top: 0,
          itemWidth: 10,
          itemHeight: 10,
          data: this.chartData.legendData,
          textStyle: {
            color: '#fff'
          }
        },
        xAxis: {
          type: 'category',
          data: this.chartData.xaxisData,
          axisTick: {
            show: false
          },
          axisLabel: {
            color: '#fff',
            interval: 0
          },
          axisLine: {
            lineStyle: {
              color: 'rgba(255, 255, 255, .4)'
            }
          }
        },
        yAxis: [
          {
            name: '（个）',
            minInterval: 1,
            nameTextStyle: {
              color: '#fff'
            },
            axisLabel: {
              color: 'rgba(255, 255, 255, .8)',
              interval: 1
            },
            type: 'value',
            splitLine: {
              show: false
            }
          },
          {
            show: false,
            axisLabel: {
              color: 'rgba(255, 255, 255, .8)',
              interval: 0,
              formatter: '{value}%'
            },
            type: 'value',
            splitLine: {
              show: false
            }
          }
        ],
        grid: {
          bottom: 10,
          left: 10,
          right: 10,
          top: 30,
          containLabel: true
        },
        series: [
          {
            name: this.chartData.legendData[0],
            type: 'bar',
            stack: '任务数',
            barMaxWidth: size,
            data: finishNumber,
            itemStyle: {
              normal: {
                color: new this.$echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: '#3381FF'
                    },
                    {
                      offset: 1,
                      color: '#66A1FF'
                    }
                  ],
                  false
                ),
                borderRadius: [4, 4, 0, 0]
              }
            }
          },
          {
            name: this.chartData.legendData[1],
            stack: '任务数',
            type: 'bar',
            barMaxWidth: size,
            // data: this.list.map((v) => v.number),
            data: this.chartData.seriesData[1],
            itemStyle: {
              normal: {
                color: new this.$echarts.graphic.LinearGradient(
                  0,
                  0,
                  0,
                  1,
                  [
                    {
                      offset: 0,
                      color: '#F7A54D'
                    },
                    {
                      offset: 1,
                      color: '#F7BD7E'
                    }
                  ],
                  false
                ),
                borderRadius: [4, 4, 0, 0]
              }
            }
          }
        ]
      }
      this.mycharts.setOption(option)
      this.mycharts.getZr().on('mousemove', () => {
        this.mycharts.getZr().setCursorStyle('default')
      })
      // this.changedata()
      window.addEventListener('resize', this.resizeChart)
    },
    resizeChart() {
      this.mycharts.resize()
    }
  }
}
</script>

<style lang="scss" scoped>
.barbox {
  width: 100%;
  height: 100%;
}

.no-data-cont {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #dddddd;

  :deep(div) {
    display: none !important;
  }
}
</style>
