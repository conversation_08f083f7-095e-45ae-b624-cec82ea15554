<!-- 综合展示=>ComprehensiveDisplay -->
<template>
  <div v-if="show" class="comprehensive-display">
    <right-time v-show="false" type="zhzs" @changeDate="changeDate" />
    <div class="mainContent">
      <div class="left">
        <panel-item
          class="xtgg"
          style="height:calc(100% - 360px); "
          title="监测评价"
          :time-str="bigScreenDatetime?.zhzs?.xtgg?.time"
          :more="true"
          @more="$router.push({ path: '/SenselessMonitoring' })"
        >
          <div class="content">
            <div class="sszbContent" style="width: 100%;height: calc(50% - 5px)">
              <panel-item title="各区营商指数" class="item-children bejing-map" theme="no-background">
                <div style="width: 100%;height: 100%;">
                  <BejingMap :year="2025" :month="7" />
                </div>
              </panel-item>
            </div>
            <div class="sszbContent" style="width: 100%;height: calc(50% - 5px);margin-top: 10px;">
              <panel-item title="实时指标" class="item-children" theme="no-background" :more="true" @more="openQuoteMore">
                <div class="quotaBox">
                  <el-carousel class="quota-time-carousel">
                    <el-carousel-item v-for="(val,idx) in quotaTimeList" :key="'quotaTimeList'+idx" class="quotaBox-carousel">
                      <div v-for="(item, index) in val" :key="item.id" class="quotaBoxItem">
                        <div class="quotaTop">
                          <img v-if="index === 0" src="../assets/images/quotaTimeIcon01.png" alt="实时指标">
                          <img v-if="index === 1" src="../assets/images/quotaTimeIcon02.png" alt="实时指标">
                          <img v-if="index === 2" src="../assets/images/quotaTimeIcon03.png" alt="实时指标">
                          <img v-if="index === 3" src="../assets/images/quotaTimeIcon04.png" alt="实时指标">
                          <img v-if="index === 4" src="../assets/images/quotaTimeIcon05.png" alt="实时指标">
                          <img v-if="index === 5" src="../assets/images/quotaTimeIcon06.png" alt="实时指标">
                          <div style="width: calc(100% - 50px);">
                            <div class="quotaNum">{{ item.value }}<span>{{ item.unit }}</span></div>
                            <TooltipWText class="quotaName" :text="item.quotaName" />
                            <!-- <div class="quotaName">{{ item.quotaName }}</div> -->
                          </div>
                        </div>
                        <div class="quotaBottomBox">
                          <div class="quotaBottom">
                            <div class="quotaRate">
                              <img v-if="item.lifting === '1'" src="../assets/images/trend-up.png" alt="">
                              <img v-if="item.lifting === '-1'" src="../assets/images/trend-down.png" alt="">
                              <span v-if="item.lifting === '0'" class="noChange">无变化</span>
                              <span>{{ item.qoqRate }}%</span>
                            </div>
                            <div class="quotaTime">{{ item.year }}年{{ item.month }}月</div>
                          </div>
                        </div>
                      </div>

                    </el-carousel-item>
                  </el-carousel>
                </div>
              </panel-item>
            </div>
          </div>
        </panel-item>
      </div>
      <div class="center">
        <div class="mapCenter">
          <BusinessEnvironmentIndex v-show="centerTabIndex === 0" />
        </div>
      </div>
      <div class="right">
        <panel-item
          class="xtgg"
          style="height:calc(100% - 360px);"
          title="协同改革"
          :time-str="bigScreenDatetime?.zhzs?.xtgg?.time"
          :more="true"
          @more="$router.push({ path: '/CollaborativeReform' })"
        >
          <div class="content">
            <CollaborativeReformWave
              ref="collaborative"
              :year.sync="xtggYear"
              :month.sync="xtggMonth"
              :params="cooperateWithInfoData"
              @openDialog="jumpTaskClick"
            />
            <!-- <CollaborativeReform ref="collaborative" :year.sync="xtggYear" :month.sync="xtggMonth" :params="cooperateWithInfoData" @openDialog="jumpTaskClick" /> -->
          </div>
        </panel-item>

        <panel-item
          class="xtgg"
          title="企业“服务包”"
          style="height:calc(100% - 360px);"
          :more="true"
          @more="$router.push({ path: '/EnterpriseServicePack' })"
        >
          <div class="content fuwubao">
            <div class="numList-box">
              <div class="numList">
                <div v-for="(v, i) in fuwubaoNumList" :key="i">
                  <label class="pangmenzhengdao">{{ v.name }}</label>
                  <p><span class="pangmenzhengdao">{{ v.num }}</span> {{ v.unit }}</p>
                </div>
              </div>
            </div>
            <ServicePackChart
              style="height: calc(100% - 70px)"
              :chart-data="fuwubaoChartDataBeijing"
              top="20"
              bottom="0"
              from="zonghezhanshi"
            />
          </div>
        </panel-item>
      </div>
    </div>

    <uq-dialog :title="'营商环境指数情况'" :show.sync="showYingshanghuanjing" width="80%" height="64vh">
      <BusinessEnvironmentExponent
        v-if="showYingshanghuanjing"
        :month="initMonth"
        :year="initYear"
        style="width:100%;height:100%;"
      />
    </uq-dialog>
    <uq-dialog :title="certificateName + '实施推广情况'" :show.sync="showCertificate" width="80%" height="50vh">
      <certificate-table v-if="showCertificate" />
    </uq-dialog>
    <uq-screen-dialog
      :class="screenWidth > 2600 ? 'business-dynamics-2600' : ''"
      title="满意度调查"
      :show-time="true"
      :show.sync="showMyd"
    >
      <degree v-if="showMyd" :init-year="initYear" :init-month="initMonth" />
    </uq-screen-dialog>
    <!-- <uq-screen-dialog title="现场核验督导" :show-time="true" :show.sync="showDd"> -->
    <uq-dialog :title="'现场核验督导'" :show.sync="showDd" width="80%" top="5vh" height="75vh">
      <verification v-if="showDd" :init-year="initYear" :init-month="initMonth" />
    </uq-dialog>

    <uq-screen-dialog title="政策详细" :show-time="true" :show.sync="showDynamic">
      <policy-details v-if="showDynamic" policy-type="政策详细" :policy-id="policyId" />
    </uq-screen-dialog>
    <uq-dialog :show.sync="showPreview" title="政策详细" :fullscreen="true">
      <preview-pdf-file v-if="previewFileId && showPreview" :id="previewFileId" />
    </uq-dialog>
    <uq-screen-dialog
      :title="listPanelTitle"
      :fullscreen="false"
      :show.sync="showTaskTodoList"
      height="90vh"
      top="5vh"
      width="80%"
    >
      <list-panel v-if="showTaskTodoList" :param="taskObj" :year="initYear" :month="initMonth" />
    </uq-screen-dialog>

    <uq-screen-dialog
      :class="screenWidth > 2600 ? 'business-dynamics-2600' : ''"
      :title="areaData.name + '营商环境综合展示'"
      :show-time="true"
      :show.sync="showArea"
    >
      <area-details
        v-if="showArea"
        ref="index"
        :task-dept-name="TaskDeptName"
        :init-year="initYear"
        :init-month="initMonth"
        from="zhzs"
        :area-data="areaData"
      />
    </uq-screen-dialog>
    <uq-screen-dialog title="问题清单" :show.sync="showProblem" :fullscreen="false" height="90vh" top="5vh" width="80%">
      <question-list v-if="showProblem" :params="problemObj" :init-year="initYear" :init-month="initMonth" />
    </uq-screen-dialog>
    <uq-dialog :show.sync="showDetailsPop" :show-title="false" width="60%" height="60vh">
      <details-pop v-if="showDetailsPop && detailsPopRow" :info="detailsPopRow" :year="initYear" :month="initMonth" />
    </uq-dialog>
    <uq-dialog class="yszbtx-dialog" title="营商指标体系" :show.sync="showIndicatorSystem" :fullscreen="true">
      <IndicatorSystem v-if="showIndicatorSystem" />
    </uq-dialog>
    <uq-screen-dialog :show.sync="quotaMoreShow" title="实时指标" :fullscreen="false" height="90vh" top="5vh" width="80%">
      <QuotaMore v-if="quotaMoreShow" :init-year="initYear" :init-month="initMonth" />
    </uq-screen-dialog>
  </div>
</template>
<script>
import AreaDetails from '../components/ComprehensiveDisplay/AreaDetails.vue'
import BusinessEnvironmentExponent from '../components/ComprehensiveDisplay/BusinessEnvironmentExponent.vue'
import CollaborativeReformWave from '../components/ComprehensiveDisplay/CollaborativeReformWave.vue'
import IndicatorSystem from '../components/ComprehensiveDisplay/IndicatorSystem.vue'
import QuotaMore from '../components/ComprehensiveDisplay/QuotaMore.vue'
import BusinessEnvironmentIndex from '../components/ComprehensiveDisplay/BusinessEnvironmentIndex.vue'
import TooltipWText from '../components/BusinessDynamics/TooltipWText.vue'

import ServicePackChart from '../components/ComprehensiveDisplay/ServicePackChart.vue'
import Degree from '../components/degree/degree.vue'
import Verification from '../components/verification/verification.vue'
import DetailsPop from '../components/InnovativeCloud/DetailsPop.vue'
import { mapActions } from 'vuex'
import { getOption, initDate } from '../api/home'
import { getBusinessInfo, getCheckSupervision, getCooperateWithInfo, getSuperviseAndHandle, getExcellentCases, getNewCasePage, queryAllServiceAppealSituationInf, getComprehensiveDisplay, getSatisfactionSurveyInit, getRealTimeQuotaPage } from '../api/ComprehensiveDisplay'
import { taskCnt, init } from '../api/gg.js'
import { listLastPeriodTopTenCase } from '../api/cxyx'
import ListPanel from '../components/ggList/ListPanel.vue'
import QuestionList from '../components/NoFinishList/QuestionList.vue'
import RightTime from '../components/common/RightTime.vue'
import PolicyDetails from '../components/BusinessDynamics/PolicyDetails.vue'
import BejingMap from '../components/HomePage/BeijingLegend.vue'
export default {
  name: 'ComprehensiveDisplay',
  components: {
    RightTime,
    QuestionList,
    ListPanel,
    Verification,
    BusinessEnvironmentExponent,
    Degree,
    PolicyDetails,
    ServicePackChart,
    TooltipWText,
    AreaDetails,
    DetailsPop,
    IndicatorSystem,
    BusinessEnvironmentIndex,
    BejingMap,
    QuotaMore,
    CollaborativeReformWave
  },
  props: {

  },
  data() {
    return {
      taskTabIndex: 1,
      qywtblqkTabIndex: 0,
      screenWidth: window.screen.width, // 屏幕宽度
      showTaskTable: true,
      quotaoOtainData: {},
      tabs: [{ name: '区级' }, { name: '市级' }],
      dateObj: null,
      show: false,
      taskObj: {},
      month: '',
      previewFileId: '',
      showPreview: false,
      showDetailsPop: false,
      detailsPopRow: {},
      showArea: false,
      currentTime: '',
      showDynamic: false,
      rText: '',
      showDd: false,
      policyId: '',
      showMyd: false,
      showCertificate: false,
      year: '',
      showImg: false,
      taskHeadData: [{ label: '任务总数', width: '110', prop: 'number', unit: '' }, { label: '推进进度', width: '110', prop: 'finishSchedule', unit: '%' }],
      qywtblqkHead: [{ label: '问题总数', width: '110', prop: 'problemNum', unit: '' }, { label: '解决率', width: '110', prop: 'progress', unit: '%' }],
      dudaoData: {
        list: [
          { name: '任务落实核验', precent: '' },
          { name: '问题整改核验', precent: '' }]
      },
      certificateName: '',
      dudaoDataCity: {
        list: [
          { name: '任务落实核验', precent: '' },
          { name: '问题整改核验', precent: '' }
        ]
      },
      fuwubaoDataBeijing: {
        'monthStr': 0,
        'CZGX_FWB': 0,
        'CC_RENT': 0,
        'Fwb_Qs_Rent': 0
      },
      fuwubaoDataChaoyang: {
        month: '1-5月',
        shouruNum: 339.1,
        shouruUnit: '亿元',
        tongbi: '3.9%',
        zhan: '65.4%',
        status: 'up'
      },
      fuwubaoNumList: [
        { name: '服务包企业', num: 0, unit: '家' },
        { name: '服务事项', num: 0, unit: '次' },
        { name: '办结率', num: 0, unit: '%' }
      ],
      fuwubaoNumListBeijing: [
        { name: '服务包企业', num: 13978, unit: '家' },
        { name: '服务事项', num: 17131, unit: '次' },
        { name: '解决率', num: 98.15, unit: '%' }
      ],
      fuwubaoNumListChaoyang: [
        { name: '服务包企业', num: 2809, unit: '家' },
        { name: '服务事项', num: 4133, unit: '次' },
        { name: '解决率', num: 99.56, unit: '%' }
      ],
      fuwubaoChartDataBeijing: {
        xAxis: [],
        data1: {
          name: '财政贡献',
          value: [],
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: '#81D6BF' // 0% 处的颜色
            }, {
              offset: 1, color: '#56D6B4' // 100% 处的颜色
            }],
            global: false // 缺省为 false
          },
          unit: '(亿元)',
          barWidth: 10
        },
        data2: {
          name: '同比',
          value: [],
          color: '#F8D151',
          unit: '(%)'
        },
        hideRightY: false,
        top: 25,
        botttom: 20,
        legendTop: 0,
        rotate: 30
      },
      fuwubaoChartDataChaoyang: {
        xAxis: ['6月', '5月', '4月', '3月', '2月', '1月'],
        data1: {
          name: '财政贡献',
          value: [],
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0, color: 'rgba(146,119,247,1)' // 0% 处的颜色
            }, {
              offset: 1, color: 'rgba(114,81,238,0.2)' // 100% 处的颜色
            }],
            global: false // 缺省为 false
          },
          unit: '亿',
          barWidth: 10
        },
        data2: {
          name: '同比',
          value: [],
          color: '#F8D152',
          unit: '%'
        },
        top: 20,
        legendTop: 0,
        rotate: 30
      },
      showGoodIndex: false,
      goodIndexData: {
        // xAxis: ['朝阳', '海淀', '大兴', '东城', '经开区', '顺义', '通州', '丰台', '门头沟', '石景山', '西城', '昌平', '房山', '密云', '怀柔', '延庆', '平谷'],
        xAxis: [],
        deadLineText: '',
        data1: {
          name: '优秀案例',
          value: [],
          // value: [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],
          color: '#ea4379',
          unit: '个',
          barWidth: 10
        },
        top: 10,
        legendTop: 0,
        rotate: 30,
        deadlineText: ''
      },
      imgData: [],
      taskTabData: {},
      imgDataBeijing: [
        { url: require('../assets/images/c1.png'), txt: '【朝阳区】实现更多市场主体“准入即准营”' },
        { url: require('../assets/images/c2.png'), txt: '【海淀区】积极推进“证照联办”改革工作' }
      ],
      imgDataChaoyang: [
        { url: require('../assets/images/c1.png'), txt: '【朝阳区】开设“朝好办”服务窗口' },
        { url: require('../assets/images/c2.png'), txt: '【朝阳区】深化国际人才一站式服务平台建设' },
        { url: require('../assets/images/c3.png'), txt: '【朝阳区】开展“一业一证”综合行政审批制度改革' },
        { url: require('../assets/images/c4.png'), txt: '【朝阳区】实现更多市场主体“准入即准营”' }
      ],
      contrastData: [],
      sourceList: [], // 重点任务进度tab
      mainTaskProgressData: null, // 重点任务进度数据
      keyTaskTips: false,
      activeTaskTab: 0,
      areatabs: [],
      areatabsBeijing: [
        { name: '商事制度改革', taskNum: 7, finish: 14, finished: 1 },
        { name: '监管执法', taskNum: 12, finish: 14, finished: 1 },
        { name: '政务服务', taskNum: 14, finish: 50, finished: 2 }
      ],
      areatabsChaoyang: [
        { name: '商事制度改革', taskNum: 2, finish: 50, finished: 1 },
        { name: '监管执法', taskNum: 5, finish: 20, finished: 1 },
        { name: '政务服务', taskNum: 5, finish: 20, finished: 1 }
      ],
      activeAreaTab: 0,

      chartDataWugan: null,
      cooperateWithInfoData: {}, // 协同改革数据

      satisfactionSurveyParams: {
        areaCode: '', // 区划编码
        areaType: '1' // 区域类型(1:北京市 3:区)
      },
      zhishi: [],
      problemObj: {},
      taskTableData: [],
      qywtblqTableData: [],
      dubanTaskData: { supervise: 0, taskProportion: 0 },
      showYingshanghuanjing: false,
      areaData: {},
      areaCode: '',
      id: this.$uid(),
      topTenCaseData: [],
      showTaskTodoList: false,
      showProblem: false,
      TaskDeptName: '',
      listPanelTitle: '改革任务清单',
      goodCaseTips: false,
      initYear: null,
      initMonth: null,
      mydTabList: [],
      currentMydTab: '',
      showIndicatorSystem: false,
      centerTabIndex: 0,
      exponentNum: null,
      bigScreenDatetime: {},
      xtggYear: '',
      xtggMonth: '',
      cxal: {},
      businessInfoData: {
        infoList: [],
        average: null
      },
      quotaTimeList: [],
      quotaMoreShow: false
    }
  },
  computed: {
    qyfwbDate() {
      return this.$formatTime(new Date(), this.bigScreenDatetime?.zhzs?.qyfwb?.dateFormat)
    }
  },
  created() {
    // 2025-1-2需求综合展示左侧协同改革部分使用协同改革页面配置月份
    initDate({ moduleCode: 'xtgg', targetCode: 'base' }).then(res => {
      this.xtggYear = res.year
      this.xtggMonth = res.month
    })
    this.getCurrentTime()
    getOption().then((res) => {
      window.sessionStorage.setItem('chartOotion', JSON.stringify(res))
      this.show = true

      this.bigScreenDatetime = JSON.parse?.(res?.['bigScreen.datetime'])
      this.cxal = JSON.parse?.(res?.['option.zhzs.cxal'])
    })
    getSatisfactionSurveyInit().then(res => {
      this.mydTabList = res || []
      this.currentMydTab = this.mydTabList?.[0]?.dmValue
    })
  },
  methods: {
    taskTabClick(data) {
      this.taskTabIndex = data.index
      this.getCityTaskProgressData()
    },
    qywtblqkTabTabClick(data) {
      this.qywtblqkTabIndex = data.index
      if (this.qywtblqkTabIndex === 0) {
        this.qywtblqTableData = JSON.parse(JSON.parse(window.sessionStorage.getItem('chartOotion'))['zhzs.qjqywtblqk'])
      } else {
        this.qywtblqTableData = JSON.parse(JSON.parse(window.sessionStorage.getItem('chartOotion'))['zhzs.sjqywtblqk'])
      }
    },
    // 点击满意度的tab
    mydTabClick(tab) {
      this.currentMydTab = tab.dmValue
    },
    getCurrentTime() {
      const now = new Date()
      const year = now.getFullYear()
      const month = now.getMonth() + 1 // 月份从0开始，需要加1
      const day = now.getDate()
      this.currentTime = year + '年' + month + '月' + day + '日'
    },
    changeDate(val) {
      this.dateObj = { year: val[0], month: val[1] }
      this.initYear = val[0]
      this.initMonth = val[1]
      this.$nextTick(() => {
        this.initData()
      })
    },
    initData() {
      // listPolicy({
      //   currentPage: this.currentPage,
      //   pageSize: 4,
      //   queryParam: {
      //     year: this.initYear,
      //     month: this.initMonth
      //   }
      // }).then((res) => {
      //   this.contrastData = res.list
      // })
      this.getCheckSupervisionFun()
      this.getCooperateWithInfoFun()
      this.getGoodIndexData()
      this.getBusinessInfoFunc()

      this.areatabs = this.areatabsBeijing
      this.imgData = this.imgDataBeijing
      this.getMainTaskTabData()
      this.getTopTenCaseData()
      this.queryAllServiceAppealSituationInf(110100)
      this.getComprehensiveDisplay(null)
      this.getQuotaList()
    },
    ...mapActions('indexPage', ['callPageTitle']),
    getBusinessInfoFunc() {
      getBusinessInfo({
        year: this.initYear,
        month: this.initMonth
      }).then(res => {
        this.businessInfoData = {
          ...res.groupInfo,
          chartConfig: JSON.parse(JSON.parse(window.sessionStorage.getItem('chartOotion'))?.['option.zhzs.gqyszs'])
        }
      })
    },
    getComprehensiveDisplay(areaCode) {
      getComprehensiveDisplay({
        'service_area_code': areaCode,
        'statistics_data_deadline': this.formatTime,
        year: this.initYear,
        month: this.initMonth
      }).then((res) => {
        if (res) {
          this.fuwubaoChartDataBeijing.xAxis = []
          this.fuwubaoChartDataBeijing.data1.value = []
          this.fuwubaoChartDataBeijing.data2.value = []
          res.datas.forEach((v) => {
            this.fuwubaoChartDataBeijing.xAxis.push(v.monthStr)
            this.fuwubaoChartDataBeijing.data1.value.push(v.CZGX_FWB ? v.CZGX_FWB.toFixed(2) : 0)
            this.fuwubaoChartDataBeijing.data2.value.push(v.CC_RENT ? v.CC_RENT.toFixed(2) : 0)
          })
          this.fuwubaoDataBeijing = res.datas[res.datas.length - 1]
        }
      })
    },
    queryAllServiceAppealSituationInf(areaCode) {
      queryAllServiceAppealSituationInf({
        'statistics_data_deadline': this.formatTime,
        year: this.initYear,
        month: this.initMonth
      }).then((res) => {
        this.fuwubaoNumList = [
          { name: '服务包企业', num: res?.['problemStatisticsInf']?.['serviceEntityCount'], unit: '家' },
          { name: '服务事项', num: res?.['problemStatisticsInf']?.['effectServiceAppealNum'], unit: '个' },
          { name: '办结率', num: res?.['problemStatisticsInf']?.['effectCompletionRate'], unit: '%' }
        ]
      })
    },
    jumpTaskClick({ data, taskTabIndex, type }) {
      if (type === 'task') {
        this.taskObj = { keyName: 'NDGGRW', type: 'ZHZS', areaCode: data.areaCode, 'leadUnit': data.name, areaType: null }
        if (data.name) this.taskObj.areaType = taskTabIndex === 0 ? '3' : '2'
        this.TaskDeptName = data.name
        this.listPanelTitle = '改革任务推进进度'
        this.showTaskTodoList = true

        this.$nextTick(() => {
          if (this.$refs.question) this.$refs.question.questionTbl()
        })
      } else {
        this.showProblem = true
      }
    },
    numClick(v) {
      if (v.name === '任务落实') {
        this.taskObj = { keyName: '', type: 'ZHZS' }
        this.listPanelTitle = '改革任务清单'
        this.showTaskTodoList = true
      } else if (v.name === '问题整改') {
        this.showProblem = true
      }
    },
    getZhishiData() {
      getNewCasePage({
        isPage: false,
        queryParam: {
          areaCode: this.areaCode,
          year: this.initYear,
          month: this.initMonth
        }
      }).then(res => {
        this.zhishi = res.list
      })
    },
    getGoodIndexData() {
      this.showGoodIndex = false
      getExcellentCases({
        year: this.initYear,
        month: this.initMonth
      }).then(res => {
        this.goodIndexData.xAxis = []
        this.goodIndexData.data1.value = []
        this.goodIndexData.deadLineText = res.dataList?.[0]?.deadLineText;
        (res.dataList || []).forEach((v) => {
          this.goodIndexData.xAxis.push(v.areaName)
          this.goodIndexData.data1.value.push(v.count)
        })
        this.showGoodIndex = true
        this.goodCaseTips = res.limitDateText
      })
    },
    getTopTenCaseData() { // 获取十佳优秀案例数据
      listLastPeriodTopTenCase({
        currentPage: 1,
        isPage: true,
        pageSize: this.screenWidth > 2600 ? 9 : 8,
        queryParam: {
          year: this.initYear,
          month: this.initMonth
        }
      }).then(res => {
        this.topTenCaseData = []
        // const remainder = this.screenWidth > 2600 ? 3 : 2
        res.dataPageResultVO.list.forEach((v, i) => {
          this.topTenCaseData.push(v)
          // if (i % remainder === 0) {
          //   this.topTenCaseData.push([v])
          // } else {
          //   this.topTenCaseData[this.topTenCaseData.length - 1].push(v)
          // }
        })
      })
    },

    getDubanTask() { // 获取督办数据
      getSuperviseAndHandle({
        areaCode: this.areaCode,
        year: this.initYear,
        month: this.initMonth
      }).then((res) => {
        this.dubanTaskData.supervise = res['taskTotal']
        this.dubanTaskData.taskProportion = res['rate']
      })
    },
    async getMainTaskTabData() { // 获取重点任务进展情况tab
      const data = {
        year: this.initYear,
        month: this.initMonth
      }
      const resInit = await init(data)
      this.sourceList = resInit['sourceList']
      this.taskTabData = await taskCnt(data)
      this.changeTab(0)
    },

    getCheckSupervisionFun() {
      getCheckSupervision({
        ...this.satisfactionSurveyParams,
        year: this.initYear,
        month: this.initMonth
      }).then(res => {
        this.dudaoDataCity = {
          list: [
            { name: '核验数量 ', precent: '', unit: '项' },
            { name: '得分', precent: '', unit: '分' }
          ]
        }
        if (!res || res.length === 0) return
        if (this.satisfactionSurveyParams.areaType === '1') { // 北京市
          this.dudaoDataCity.list[0].precent = String(res.find(item => item.dataType === '04')?.rate)
          this.dudaoDataCity.list[1].precent = String(res.find(item => item.dataType === '05')?.rate)
          this.dudaoData = this.dudaoDataCity
        }
        if (this.dudaoData?.list) {
          // this.dudaoData['list'].forEach(d => {
          //   const list = d.name.split('')
          //   list.splice(2, 0, '<br>')
          //   d.name = list.join('')
          // })
        }
        this.$forceUpdate()
      })
    },
    getCooperateWithInfoFun() {
      getCooperateWithInfo({
        ...this.satisfactionSurveyParams,
        year: this.xtggYear,
        month: this.xtggMonth
      }).then(res => {
        this.cooperateWithInfoData = res
      })
    },
    changeTab(index) {
      this.activeTaskTab = index
      const key = this.sourceList[index].dmValue
      this.mainTaskProgressData = this.taskTabData[key]
    },

    // 营商动态
    dtClick(i, v) {
      if (v.docId) {
        this.previewFileId = v.docId
        this.showPreview = true
      } else {
        this.policyId = v.id
        this.showDynamic = true
      }
    },
    certificateClick(v) {
      if (!v) return
      this.certificateName = v.name
      if (v.name === '一业一证') {
        this.showCertificate = true
      } else if (v.name === '一件事集成服务') {
        this.showImg = true
        // this.tableData = this.yjsjcfwData
      } else if (v.name === '一体化综合监管') {
        this.showImg = true
        // this.tableData = this.ythzhjgData
      }
    },

    back() {
      this.showArea = !this.showArea
      this.satisfactionSurveyParams = {
        areaCode: '',
        areaType: '1'
      }
      this.areaCode = ''
      this.getCheckSupervisionFun()
      this.getCityTaskProgressData()
      this.areatabs = this.areatabsBeijing
      this.activeTaskTab = 0
      this.activeAreaTab = 0
      this.imgData = this.imgDataBeijing
      // this.fuwubaoNumList = this.fuwubaoNumListBeijing
      this.queryAllServiceAppealSituationInf(110100)
      this.getComprehensiveDisplay(110100)
      this.callPageTitle('北京市营商环境综合展示')
      this.id = this.$uid()
      this.changeTab(0)
      this.getTopTenCaseData()
      this.getGoodIndexData()
      this.satisfactionSurveyParams = {
        areaCode: '',
        areaType: '2'
      }
      this.getCooperateWithInfoFun()
    },
    goArea(params) {
      this.areaData = params.data
      this.showArea = true
    },
    formatter(num) {
      if (num === 0) return num
      if ((num + '').indexOf('.') !== -1) {
        return num.toFixed(2)
      } else {
        return num
      }
    },
    caseClick(item) {
      this.detailsPopRow = item
      this.showDetailsPop = true
    },
    async getQuotaList() {
      const param = {
        currentPage: 1,
        pageSize: 1000,
        queryParam: {
          // year: this.initYear,
          // month: this.initMonth
        }
      }
      const resultArray = []
      const res = await getRealTimeQuotaPage(param)
      for (let i = 0; i < res.list.length; i += 6) {
        const chunk = res.list.slice(i, i + 6)
        resultArray.push(chunk)
      }
      this.quotaTimeList = resultArray
    },
    openQuoteMore() {
      this.quotaMoreShow = true
    }
  }
}
</script>

<style scoped lang='scss'>
.bejing-map{
  :deep(.pi-title){
    height: 40px !important;
  }
  :deep(.pi-content) {
    padding-top: 30px !important;
  }
}
.item-children {
  box-shadow: 0 0 16px 0 rgba(40, 121, 197, 0.5) inset;
  :deep(.pi-title){
      font-size: 20px !important;
      background-position: left 10px top 20px !important;
  }
  :deep(> .pi-content) {
    border: 1px solid #0f4271 !important;
    border-radius: 6px !important;
  }
}

.xtgg {
  min-height: 205px;

  :deep(> .pi-content) {
    >.panel-content {
      padding-top: 26px !important;

      .content {
        padding: 10px;
        box-sizing: border-box;
      }
    }
  }
}

.comprehensive-display {
  overflow: hidden;
  height: 100%;
}

.mainContent {
  padding-top: 10px;
  box-sizing: border-box;
  display: flex;
  gap: 10px;
  height: 100%;
}

.right {
  .goodIndex {
    height: calc(100% - 160px);
  }
}

.left,
.right {
  display: flex;
  flex-direction: column;
  gap: 10px;
  width: 24%;
  height: 100%;
  min-height: 100%;
  margin-top: -10px;

  >div {
    background: rgba(96, 143, 233, 0.15);
    border-radius: 12px;
  }

  .content {
    height: 100%;
    border-top: 0;
    // margin-top: -3px;
  }
}

.left>div {
  box-sizing: border-box;
  flex: 1;

  &:last-child {
    margin-bottom: 0;
  }
}

.right>div {
  // margin-bottom: 0.8vh;
  // padding: 10px;
  box-sizing: border-box;
}

.center {
  flex: 1;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.titbox {
  padding-left: 3.6vh;
  background-size: contain;
  background: url("../assets/img/c/circle.svg") no-repeat 10px center;
  margin-bottom: 5px;
  color: #ffffff;
  justify-content: space-between;

  .tit {
    font-size: 24px;
    font-family: "PangmenZhengdao", serif !important;
    font-style: normal;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    zoom: 1;
    letter-spacing: 2px;

    span {
      font-size: 21.6px;
    }
  }

  .extra {
    font-size: 17.28px;
    color: #18c7fe;
  }
}

.together {
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: space-between;
  box-sizing: border-box;
  padding: 5px 20px;
  font-size: 16px;
  gap: 10px;

  >div {
    width: 100%;
    height: 50%;
    padding: 5px 0;
    text-align: center;
    display: flex;
    flex-direction: row;
    align-items: center;
    position: relative;
    inset: 0;
    box-sizing: border-box;

    &:after,
    &:before {
      position: absolute;
      content: "";
      display: block;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
      border-radius: 4px;
      z-index: 1;
    }

    &:after {
      z-index: 2;
      height: calc(100% - 1px);
      width: calc(100% - 2px);
      background: linear-gradient(180deg, #0b3a5e, #122549 100%);
    }

    &:before {
      left: -1px;
      top: 0;
      border-radius: 4px;
      /*border: 1px solid #1364a2;*/
      background: linear-gradient(180deg, rgba(11, 58, 94, 0), #1364a2 100%);
    }

    >* {
      position: relative;
      z-index: 3;
    }

    div:not(.line) {
      flex: 1;
    }

    .title {
      zoom: 1;
      width: 100px;
      display: flex;
      flex-direction: column;

      img {
        width: 40px;
      }

      justify-content: center;
      align-items: center;

      span {
        letter-spacing: 2px;
        font-size: 20px;
      }
    }

    .rate {
      flex: 2;
    }

    .num {
      cursor: pointer;
      color: #84aeff;
      font-family: "PangmenZhengdao", serif !important;
      font-style: normal;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      zoom: 1;
      letter-spacing: 2px;
      font-size: 25.92px;
    }

    .line {
      width: 1px;
      height: 100%;
      margin-left: 10px;
      background: linear-gradient(180deg,
          #20cff200 0%,
          #20d0f2 50%,
          #20cff200 100%);
    }

    .percent {
      color: #f8d151;
      font-family: "PangmenZhengdao", serif !important;
      font-style: normal;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      zoom: 1;
      letter-spacing: 2px;
      font-size: 25.92px;
    }
  }
}

.d-flex {
  display: flex;
}

.dudao-content {
  padding-top: 5px !important;
  padding: 5px 15px;
  padding-bottom: 10px;
  height: calc(100% - 20px) !important;
}

.dudaoList {
  width: 100%;
  display: flex;
  padding: 5px 10px;
  margin-top: 10px;
  height: calc(100% - 10px);
  box-sizing: border-box;
  justify-content: space-between;
  align-items: center;
  background: linear-gradient(180deg, #0b3a5e, rgba(11, 58, 94, 0) 100%);
  border: 1px solid;
  border-image: linear-gradient(180deg, rgba(22, 114, 175, 0), #1672af 100%) 0.8780487775802612 0.8780487775802612;

  >div.dudao-item {
    display: flex;
    align-items: center;
    height: 70%;
    flex: 1;

    img {
      width: 4vh;
    }

    .dudao-item-border {
      width: 2px;
      height: 100%;
      background: linear-gradient(to top,
          rgba(255, 255, 255, 0) 0%,
          #248fb1 50%,
          rgba(255, 255, 255, 0) 100%);
    }

    >div.dudao-item-individual {
      margin-left: 10px;
      font-size: 16px;
      flex: 1;
      display: flex;
      height: 100%;
      align-items: center;
      justify-content: space-between;
      flex-direction: column;
    }

    p.dudao-name {
      text-align: center;
    }

    span.unit {
      padding-right: 10px;
    }

    p.precent {
      color: #edbe2b;
      font-family: "PangmenZhengdao", serif !important;
      font-style: normal;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      zoom: 1;
      text-align: center;
      letter-spacing: 2px;
      font-size: 25.92px;

      span {
        font-family: "PangmenZhengdao", serif !important;
        font-size: 19.44px;

        &.animate-number {
          font-size: 25.92px;
        }
      }

      &.d0 {
        color: #84aeff;
      }

      &.d1 {
        color: #f8d151;
      }
    }

    // &:nth-child(2) {
    //   margin: 5px 0;
    // }
  }
}

.circleProgress {
  flex: 1;
  position: relative;

  .txt {
    width: 100%;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    text-align: center;

    p {
      color: #f8d151;
      font-family: "PangmenZhengdao", serif !important;
      font-style: normal;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      zoom: 1;
      letter-spacing: 2px;
      font-size: 16px;

      span {
        font-family: "PangmenZhengdao", serif !important;
        font-size: 12.96px;
      }
    }

    div {
      font-family: "PangmenZhengdao", serif !important;
      font-style: normal;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      zoom: 1;
      letter-spacing: 1px;
      font-size: 17.28px;
    }
  }
}

.fuwubao {
  box-sizing: border-box;

  .numList-box {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 0 0 10px 0;

    .time-icon {
      width: 20px;
      height: 20px;
    }

    .numList {
      flex: 1;
      display: flex;
      justify-content: space-between;
      background: url("../assets/images/qyfwb-bg.png") no-repeat center center;
      background-size: 100% 100%;

      label {
        font-size: 19.44px;
      }

      div {
        width: 33%;
        height: 63px;
        // background: url("../assets/images/fuwubao1.png") no-repeat center center;
        background-size: 90% 90%;
        font-size: 16.2px;
        text-align: center;
        line-height: 30px;

        span {
          color: #f4af1b;
          font-size: 21.6px;
        }
      }
    }
  }

  .numTxt {
    flex: 1;
    height: 50px;
    // display: flex;
    flex-wrap: wrap;
    align-items: center;
    word-break: keep-all;
    font-weight: bold;
    background: linear-gradient(90deg,
        rgba(30, 47, 104, 0) 0%,
        #1e2f68 50%,
        rgba(30, 47, 104, 0) 100%);
    font-size: 14px;
    // line-height: 3vh;
    margin: 0.5vh 0;

    .zqs-span {
      display: block;
      padding-left: 17px;
    }

    i:first-child {
      margin-right: -8px;
      color: rgba(243, 177, 27, 0.5);
    }

    i {
      color: rgba(243, 177, 27, 1);
    }

    .blue {
      color: #01b9f4;
    }

    .yellow {
      color: #ffcf29;
    }

    .down-color {
      color: #0efeff;
    }

    .up-color {
      color: #ff353a;
    }

    .green {
      color: #2dcb59;
    }
  }
}

.ttbox {
  padding-left: 10px;
  box-sizing: border-box;

  .cir {
    margin-right: 5px;
  }

  .text,
  &.text {
    flex: 1;
    color: #29f1fa;
    gap: 0 5px;
    font-size: 16px;

    &:after {
      display: block;
      background: rgba(40, 121, 197, 0.289);
      height: 2px;
      content: "";
      flex: 1;
    }
  }
}

.ai-center {
  align-items: center;
}

.jc-center {
  justify-content: center;
}

.flex-1 {
  flex: 1;
  overflow: hidden;
}

.chuanxin {
  box-sizing: border-box;

  .ggcxal {
    margin: 10px auto 0;
    width: 416px;
    height: 44px;
    background: url("../assets/images/ggcxal.png") no-repeat center center;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    color: #f3f8fc;
    font-family: "PangmenZhengdao", serif !important;

    span {
      color: #f4af1b;
      font-size: 24px;
      margin: 0 10px;
      font-family: "PangmenZhengdao", serif !important;
    }
  }

  .lunbo {
    height: calc(100% - 54px);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .el-carousel {
    width: 100%;
  }

  :deep(.el-carousel) {
    .el-carousel__container {
      margin: 5px 0 5px;
      margin-top: 20px;
    }

    .el-carousel__indicator button {
      width: 24px;
      height: 3px;
      background: #0963bf;
      border-radius: 2px 2px 1px 1px;
    }

    .el-carousel__indicator.is-active button {
      background: #beeaff;
    }

    .el-carousel__item {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;
      box-sizing: border-box;

      .el-carousel__mask {
        background-color: initial;
      }

      div.good-case {
        width: 100%;
        height: 100%;
        position: relative;
        border: 2px solid #172b48;
        cursor: pointer;

        .el-image {
          height: 100%;
          width: 100%;
        }

        img {
          height: 100%;
          width: 100%;
        }

        .explain {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          height: 30px;
          background-image: linear-gradient(to bottom,
              rgba(255 255 255 / 50%),
              rgb(0 0 0 / 80%),
              rgba(0 0 0));
          padding: 10px;
          display: flex;

          div {
            width: 2px;
            height: 12px;
            background: #fa9014;
            margin-top: 2px;
          }

          p {
            color: #fff;
            font-size: 10px;
            overflow: hidden;
            text-overflow: ellipsis;
            -webkit-line-clamp: 2;
            display: -webkit-box;
            -webkit-box-orient: vertical;
          }
        }

        // p {
        //   position: absolute;
        //   height: 3.6vh;
        //   background: rgba($color: #000000, $alpha: 0.5);
        //   bottom: 0;
        //   width: 100%;
        //   font-size: 10px;
        //   overflow: hidden;
        //   text-overflow: ellipsis;
        //   -webkit-line-clamp: 2;
        //   display: -webkit-box;
        //   -webkit-box-orient: vertical;
        // }
      }
    }
  }
}

.duibiItem {
  flex: 1;
  display: flex;
  background: linear-gradient(0deg,
      rgba(24, 56, 141, 0) 0%,
      rgba(55, 159, 224, 0.5) 100%);
  border-radius: 4px;
  align-items: center;
  padding: 5px;
  padding-left: 30px;
  box-sizing: border-box;
  position: relative;
  margin-bottom: 5px;
  cursor: pointer;

  span {
    position: absolute;
    left: 5px;
    width: 1.2vh;
    height: 1.2vh;
    border: 4px solid rgb(23 56 67 / 64%);
    border-radius: 50%;
    background: #3c86f5;
    display: block;
  }

  div {
    width: 100%;
    font-size: 15px;
  }

  &:last-child {
    margin-bottom: 0;
  }
}

.yingshang {
  box-sizing: border-box;
}

.mapCenter {
  position: relative;
  background: rgba(6, 21, 49, 0.6);
  height: calc(100% - 275px);

  // margin-bottom: 10px;
  // flex: 1;
  .center-tab {
    z-index: 100;
    position: absolute;
    top: -20px;
    // right: 0;
    display: flex;
    gap: 10px;
    // bottom: 0;
    left: 0;

    // transform: translateX(calc(-50%));
    .ct-item {
      font-weight: 600;
      cursor: pointer;
      text-align: center;
      line-height: 35px;
      width: 134px;
      height: 40px;
      background: url(../assets/images/yszbtx/tab-bg.png) no-repeat center;
      background-size: 100% 100%;
      color: #267ac8;
      text-shadow: 0px 2px 2px rgba(4, 29, 64, 0.7);
      display: flex;
      // align-items: center;
      justify-content: center;
      font-size: 14px;
      gap: 3px;

      &.active {
        background: url(../assets/images/yszbtx/tab-bg-active.png) no-repeat center;
        background-size: 100% 100%;
        color: #79e1ff;
        text-shadow: 0px 2px 2px rgba(4, 29, 64, 0.7);
      }

      img {
        width: 14px;
        height: 11px;
        margin-top: 13px;
      }
    }
  }
}

.progressCenter {
  height: 275px;
  gap: 10px;
  margin-top: -15px;

  .wgnj-content {
    height: 235px;
    display: flex;
    justify-content: space-between;

    >div:nth-child(1) {
      margin-right: 10px;
    }

    >div {
      flex: 1;
      display: flex;
      flex-direction: column;

      :deep(.pi-title) {
        span {
          font-family: "Helvetica Neue", Helvetica, "PingFang SC",
            "Hiragino Sans GB", "Microsoft YaHei", "微软雅黑", Arial, sans-serif !important;
          font-weight: 600;
          color: #66d5ed;
          font-size: 16px;
        }
      }
    }
  }

  .zhzs-title {
    width: 100%;
    background: url(../assets/images/zhzs-title.png) no-repeat center;
    background-size: 100% 100%;
    padding: 0px 0px 8px 45px;
    margin-bottom: 8px;
    box-sizing: border-box;
    font-size: 24px;
  }
}

.nums {
  padding: 10px;
  box-sizing: border-box;
  background: url("../assets/images/mainBg.png") no-repeat 5px 5px;
  background-size: 98% 98%;
  margin-top: 5px;
  height: calc(100% - 62px);

  >.line {
    height: 1px;
    background: linear-gradient(90deg,
        rgba(32, 208, 242, 0) 0%,
        rgba(32, 208, 242, 0.56) 50%,
        rgba(32, 208, 242, 0) 100%);
    margin: 10px 0;
  }

  .nums1,
  .nums2 {
    height: calc(50% - 2px);
  }

  .nums1 {
    display: flex;

    .line {
      width: 1px;
      background: linear-gradient(180deg,
          rgba(32, 208, 242, 0) 0%,
          rgba(32, 208, 242, 0.56) 50%,
          rgba(32, 208, 242, 0) 100%);
    }

    .numsItem {
      flex: 1;
      display: flex;
      text-align: center;
      justify-content: center;
      align-items: center;
      font-size: 20px;

      img {
        width: 5vh;
        margin-right: 10px;
      }

      p {
        color: #f8d151;
        font-family: "PangmenZhengdao", serif !important;
        font-style: normal;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
        zoom: 1;
        letter-spacing: 2px;
        font-size: 30px;
      }
    }
  }

  .nums2 {
    display: flex;

    .line {
      width: 1px;
      background: linear-gradient(180deg,
          rgba(32, 208, 242, 0) 0%,
          rgba(32, 208, 242, 0.56) 50%,
          rgba(32, 208, 242, 0) 100%);
    }

    .numsItem {
      flex: 1;
      text-align: center;
    }

    p {
      color: #21bff4;
      font-family: "PangmenZhengdao", serif !important;
      font-style: normal;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      zoom: 1;
      letter-spacing: 2px;
      font-size: 30px;
    }

    .progress {
      flex: 1;
      display: flex;
      align-items: center;
      font-size: 20px;
      padding: 0 2vh;

      >div {
        flex: 1;
        height: 2vh;
        margin: 0 10px;
        background: rgba(255, 255, 255, 0.1);

        div {
          background: linear-gradient(90deg, #5a3fff, #1ed6ff 100%);
          height: 100%;
          position: relative;

          &::after {
            height: calc(100% + 2px);
            display: block;
            content: "";
            position: absolute;
            width: 1px;
            background: white;
            right: 0;
            top: -1px;
          }
        }
      }

      span {
        color: #20d1ff;
      }
    }
  }
}

.more {
  cursor: pointer;
  color: #46e4f0;
  font-size: 17.28px;
  user-select: none;
}

.zhishiItem {
  background: rgba(34, 137, 202, 0.18);
  border-radius: 4px;
  padding: 0.3vh 2vh;
  box-sizing: border-box;
  margin-top: 5px;

  .zhishiHead {
    display: flex;

    .zhishiIcon {
      background: linear-gradient(180deg, #042c69, #0257ad 100%);
      border: 1.34px solid #87b4ed;
      border-radius: 5px;
      box-shadow: 0px 0px 6.71px 0px #649ef9;
      width: 7vh;
      height: 5.5vh;
      font-size: 12.96px;
      margin-right: 2vh;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      img {
        width: 3vh;
      }
    }

    .zhishiTitle {
      flex: 1;
      font-size: 19px;
      font-family: PingFang SC, PingFang SC-Medium;
      font-weight: 500;
      color: #ffffff;
      border-bottom: 1px solid rgba(255, 255, 255, 0.4);
    }
  }

  .zhishiContent {
    font-size: 1.4px;
    margin: 0.7vh 0;
    overflow: hidden;
    text-overflow: ellipsis;
    -webkit-line-clamp: 2;
    display: -webkit-box;
    -webkit-box-orient: vertical;
  }
}

.analysis-table.display {
  .dept-name {
    white-space: nowrap;
    word-break: keep-all;
  }

  :deep(.el-table) {
    th.el-table__cell {
      color: #fffff5;
      border: none;
      background: #0a1b4b !important;

      &.align-left div {
        padding-left: 35px;
      }
    }
  }

  span.dot {
    width: 6px;
    height: 6px;
    border: 4px solid rgba(23, 56, 67, 0.64);
    border-radius: 50%;
    background: #3c86f5;
    display: block;
    margin-right: 5px;
    margin-left: 10px;
    float: left;
    margin-top: 4px;
  }
}

.yjsjcfw-nav {
  display: flex;

  // height: 16vh;
  // padding-top: 2.4vh;
  .line-height {
    width: 1px;
    background: linear-gradient(180deg,
        rgba(32, 208, 242, 0) 0%,
        rgba(32, 208, 242, 0.56) 50%,
        rgba(32, 208, 242, 0) 100%);
  }

  img {
    width: 4.4vh;
    height: 4.4vh;
    margin-right: 25px;
  }

  .nums-1 {
    width: calc(50% - 3px);
    display: inline-flex;
    justify-content: center;
    align-items: center;
    padding-top: 12px;
  }

  p {
    text-align: center;
    font-size: 30.24px;
    font-weight: 600;
  }

  .numsItem+.numsItem {
    margin-top: 0.7vh;
  }

  .nums-2 {
    width: calc(50% - 3px);
    display: inline-flex;
    justify-content: center;
    align-items: center;
    padding-top: 12px;

    >div {
      display: flex;
      height: 100%;
      text-align: center;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
  }
}

.select-numsItem {
  user-select: none;
}

.scene-verification {
  height: 145px;
  padding-bottom: 10px;

  .dudao-content {
    height: 100px !important;
  }
}

.progressBox {
  display: flex;
  width: 100%;
  align-items: center;

  :deep(.el-progress) {
    width: calc(100% - 80px);
    margin-right: 10px;

    .el-progress-bar__outer {
      height: 8px !important;
    }

    .el-progress-bar__inner {
      background: linear-gradient(90deg, #d85487 0%, #fbe947 100%);
    }
  }
}

.numsItem {
  font-size: 20px;

  >p {
    margin-top: 15px;
  }
}
</style>
<style lang="scss">
.myd-panel {
  .panel-content {
    display: flex;
    flex-direction: column;
  }

  .myd-tab {
    padding: 0 10px;
    margin-top: 5px;

    .tabs-item {
      span {
        font-size: 12px !important;
        white-space: pre-wrap;
        padding: 0 2px;
        display: flex !important;
        justify-content: center;
        align-items: center;
        line-height: 1;
      }
    }
  }
}

.img-dialog {
  .el-dialog__header {
    padding: 0;
  }

  .el-dialog__body {
    padding: 0;
    position: relative;

    img {
      position: absolute;
      top: 0;
      right: 0;
      left: 0;
      width: 100%;
      bottom: 0;
    }

    .img-close-dialog {
      width: 30px;
      height: 30px;
      position: absolute;
      top: 16px;
      right: 38px;
      font-size: 18px;
      cursor: pointer;
    }
  }
}

.yingshang-duibi {
  overflow: hidden;
  display: flex;
  flex-direction: column;
  margin-top: 8px;
  position: relative;
  height: calc(100% - 40px);
  padding: 0 10px 10px 10px;

  .db-carousel {
    height: 100%;
  }

  .el-carousel {
    height: 100%;
  }

  .el-carousel__container {
    height: 100%;
  }
}

.no-data {
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.yszbtx-dialog {
  .el-dialog__body {
    padding: 0 !important;

    .dialog-content {
      overflow: hidden;
    }
  }
}

.sszbContent .panel-content {
  padding-top: 3px !important;
}

.quotaBox {
  width: 100%;
  height: 100%;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;

  .quota-time-carousel{
      height: 100%;
      width:100%;
      overflow: hidden;
      .el-carousel__container{
        height: 100%;
      }
      .el-carousel__indicators--horizontal{
        display: none !important;
      }
      .el-carousel__arrow i{
        font-size: 24px;
      }
      .el-carousel__arrow--right{
        right: 5px;
        &:hover{
          background-color: rgba(31, 45, 61, .11);
        }
      }
      .el-carousel__arrow--left{
        left: 5px;
        &:hover{
          background-color: rgba(31, 45, 61, .11);
        }
      }
    }

  .quotaBox-carousel{
      display: flex;
      flex-wrap: wrap;
      height: 100%;
      width:100%;
      gap: 0px 8px;
      // justify-content: center;
    }

  // gap: 2%;
  .quotaBoxItem {
    width: 49%;
    height: 33%;
    background: url('../assets/images/quotaItemBg.png') no-repeat;
    background-size: 100% 100%;
    padding: 0.8vw 13px 5px 0;
    box-sizing: border-box;
    position: relative;

    .quotaTop {
      display: flex;
      width: 100%;
      justify-content: flex-start;

      img {
        height: 45px;
        margin: 0px 13px 0 21px;
      }

      .quotaNum {
        font-family: PangMenZhengDao;
        font-weight: 400;
        width: 100%;
        font-size: 24px;
        color: #f4af1b;
        margin-top: 14px;
        margin-bottom: 0.5vw;

        span {
          font-family: Microsoft YaHei;
          font-weight: 400;
          font-size: 18px;
          color: #f4af1b;
          margin-left: 6px;
        }
      }

      .quotaName {
        font-size: 14px;
        line-height: 16px;
        padding-top: 10px;
        color: #FFFFFF;
        height: 40px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }

    .quotaBottomBox {
      position: absolute;
      bottom: 14px;
      left: 0;
      width: 100%;
    }

    .quotaBottom {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .quotaRate {
        img {
          width: 10px;
          height: 13px;
          vertical-align: middle;
          margin: 0 4px 0 18px;
        }

        .noChange {
          vertical-align: middle;
          margin: 0 4px 0 18px;
        }

        span {
          vertical-align: middle;
          font-family: Microsoft YaHei;
          font-weight: 400;
          font-size: 14px;
          color: #FFFFFF;
        }
      }

      .quotaTime {
        font-family: Microsoft YaHei;
        font-weight: 400;
        font-size: 12px;
        color: #5ABAFE;
        margin-right: 12px;
      }
    }
  }
}

@media (max-height: 1200px) {
  .quotaBox {
    width: 100%;
    height: 100%;
    display: flex;
    flex-wrap: wrap;

    // gap: 2%;
    .quotaBoxItem {
      width: 49%;
      height: 33%;
      background: url('../assets/images/quotaItemBg.png') no-repeat;
      background-size: 100% 100%;
      padding: 0.8vw 13px 5px 0;
      box-sizing: border-box;
      position: relative;

      .quotaTop {
        display: flex;
        justify-content: flex-start;

        img {
          height: 46px;
          margin: 0px 13px 0 7px;
        }

        .quotaNum {
          font-family: PangMenZhengDao;
          font-weight: 400;
          font-size: 26px;
          color: #f4af1b;
          margin-top: 0px;
          margin-bottom: 0.5vw;

          span {
            font-family: Microsoft YaHei;
            font-weight: 400;
            font-size: 14px;
            color: #f4af1b;
            margin-left: 6px;
          }
        }

        .quotaName {
          color: #FFFFFF;
          height: 40px;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .quotaBottomBox {
        position: absolute;
        bottom: 5px;
        left: 0;
        width: 100%;
      }

      .quotaBottom {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .quotaRate {
          img {
            width: 10px;
            height: 13px;
            vertical-align: middle;
            margin: 0 4px 0 18px;
          }

          .noChange {
            vertical-align: middle;
            margin: 0 4px 0 18px;
          }

          span {
            vertical-align: middle;
            font-family: Microsoft YaHei;
            font-weight: 400;
            font-size: 14px;
            color: #FFFFFF;
          }
        }

        .quotaTime {
          font-family: Microsoft YaHei;
          font-weight: 400;
          font-size: 12px;
          color: #5ABAFE;
          margin-right: 12px;
        }
      }
    }
  }
}

@media (max-height: 1000px) {
  .quotaBox {
    width: 100%;
    height: 100%;

    // gap: 2%;
    .quotaBoxItem {
      width: 49%;
      height: 33%;
      background: url('../assets/images/quotaItemBg.png') no-repeat;
      background-size: 100% 100%;
      padding: 0.5vw 13px 5px 0;
      box-sizing: border-box;
      position: relative;

      .quotaTop {
        display: flex;
        justify-content: flex-start;

        img {
          height: 46px;
          margin: 0px 6px 0 7px;
        }

        .quotaNum {
          font-family: PangMenZhengDao;
          font-weight: 400;
          font-size: 26px;
          color: #f4af1b;
          margin-top: 0px;
          margin-bottom: 0;

          span {
            font-family: Microsoft YaHei;
            font-weight: 400;
            font-size: 14px;
            color: #f4af1b;
            margin-left: 6px;
          }
        }

        .quotaName {
          color: #FFFFFF;
          height: 40px;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }

      .quotaBottomBox {
        position: absolute;
        bottom: 5px;
        left: 0;
        width: 100%;
      }

      .quotaBottom {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .quotaRate {
          img {
            width: 10px;
            height: 13px;
            vertical-align: middle;
            margin: 0 4px 0 18px;
          }

          .noChange {
            vertical-align: middle;
            margin: 0 4px 0 18px;
          }

          span {
            vertical-align: middle;
            font-family: Microsoft YaHei;
            font-weight: 400;
            font-size: 14px;
            color: #FFFFFF;
          }
        }

        .quotaTime {
          font-family: Microsoft YaHei;
          font-weight: 400;
          font-size: 12px;
          color: #5ABAFE;
          margin-right: 12px;
        }
      }
    }
  }
}
</style>
